# Publishing the Design System

This document explains how to publish new versions of the Design System to GitHub Package Registry and how to consume them in web projects.

## Overview

The Design System uses GitHub Actions to build and publish NPM packages to GAHR's private GitHub Package Registry. This ensures:

- **Version control**: Only new versions can be published
- **Quality assurance**: Tests run before publishing
- **Consistent builds**: Standardized build process
- **Private distribution**: Packages are only available to GAHR organization members

## Publishing Process

### 1. Prepare for Release

Before publishing, ensure your changes are ready:

```bash
# Run tests locally
npm test

# Build and verify locally
npm run build

# Check what files will be included
npm run test-package
```

### 2. Update Version

Use semantic versioning to update the package version:

```bash
# For bug fixes (1.0.0 → 1.0.1)
npm run version:patch

# For new features (1.0.0 → 1.1.0)
npm run version:minor

# For breaking changes (1.0.0 → 2.0.0)
npm run version:major
```

This will:
- Update `package.json` and `package-lock.json`
- Create a git commit with the version change
- Create a git tag

### 3. Push Changes

```bash
# Push the version commit and tag
git push && git push --tags
```

### 4. Trigger the Workflow

1. Go to your repository on GitHub
2. Navigate to the **Actions** tab
3. Select **"Publish Design System Package"**
4. Click **"Run workflow"**
5. Optionally check **"Run in dry-run mode"** to test without publishing
6. Click **"Run workflow"** to start

### 5. Monitor the Build

The workflow will:
- ✅ Check that the version doesn't already exist
- ✅ Install dependencies
- ✅ Run tests
- ✅ Build the package
- ✅ Verify all required files are present
- ✅ Publish to GitHub Package Registry
- ✅ Create a summary with installation instructions

## Consuming the Package

### Authentication Setup

To install packages from GitHub Package Registry, you need authentication:

#### Option 1: Global Configuration

```bash
# Set registry for @psprowls scope
npm config set @psprowls:registry https://npm.pkg.github.com

# Set authentication token
npm config set //npm.pkg.github.com/:_authToken YOUR_GITHUB_TOKEN
```

#### Option 2: Project .npmrc File

Create a `.npmrc` file in your project root:

```
@psprowls:registry=https://npm.pkg.github.com
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}
```

Then set the environment variable:

```bash
export GITHUB_TOKEN=your_github_token_here
```

### Creating a GitHub Token

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with `read:packages` permission
3. Copy the token and use it in your configuration

### Installation

```bash
# Install latest version
npm install @psprowls/design-system@latest

# Install specific version
npm install @psprowls/design-system@1.2.3

# Install and save to dependencies
npm install --save @psprowls/design-system@latest
```

### Usage in Web Projects

#### Vanilla JavaScript/HTML

```html
<!-- Include the design system -->
<script type="module" src="node_modules/@psprowls/design-system/dist/gahr/gahr.esm.js"></script>

<!-- Use components -->
<gahr-button>Click me</gahr-button>
<gahr-card>
  <h2>Card Title</h2>
  <p>Card content</p>
</gahr-card>
```

#### React Projects

```jsx
import React from 'react';
import { GahrButton, GahrCard } from '@psprowls/design-system/react';

function App() {
  return (
    <div>
      <GahrButton onClick={() => console.log('clicked')}>
        Click me
      </GahrButton>
      <GahrCard>
        <h2>Card Title</h2>
        <p>Card content</p>
      </GahrCard>
    </div>
  );
}
```

#### Next.js Projects

```jsx
// pages/_app.js or app/layout.js
import { defineCustomElements } from '@psprowls/design-system/loader';
import { useEffect } from 'react';

export default function App({ Component, pageProps }) {
  useEffect(() => {
    defineCustomElements();
  }, []);

  return <Component {...pageProps} />;
}
```

## Package Structure

The published package includes:

```
@psprowls/design-system/
├── dist/
│   ├── index.js              # ES module entry point
│   ├── index.cjs.js          # CommonJS entry point
│   ├── types/                # TypeScript definitions
│   ├── react/                # React components
│   ├── loader/               # Custom elements loader
│   ├── hydrate/              # SSR hydration
│   └── gahr/                 # Stencil build output
└── package.json
```

## Available Entry Points

- **Main**: `@psprowls/design-system`
- **React**: `@psprowls/design-system/react`
- **Loader**: `@psprowls/design-system/loader`
- **Hydrate**: `@psprowls/design-system/hydrate`

## Troubleshooting

### "Version already exists" Error

Update the version number in `package.json` and try again:

```bash
npm run version:patch
git push && git push --tags
```

### Authentication Errors

Ensure your GitHub token has `read:packages` permission and is correctly configured.

### Build Failures

Check the Actions tab for detailed error logs. Common issues:
- Test failures
- Missing dependencies
- Build configuration errors

### Installation Issues

Verify:
- GitHub token is valid and has correct permissions
- Registry is correctly configured
- Package name and version are correct

## Best Practices

1. **Always test locally** before publishing
2. **Use semantic versioning** appropriately
3. **Write meaningful commit messages** for version updates
4. **Test the dry-run** before actual publishing
5. **Document breaking changes** in release notes
6. **Coordinate with web team** before major version updates

## Support

For issues with the publishing process or package consumption, contact the development team or create an issue in the repository.
