# GitHub Pages Setup Guide

This guide explains how to configure GitHub Pages for the Design System demo site deployment.

## Prerequisites

- Repository must be public or have GitHub Pro/Team/Enterprise for private repo Pages
- Admin access to the repository
- The `deploy-demo-site.yml` workflow file must be present in `.github/workflows/`

## Repository Configuration

### 1. Enable GitHub Pages

1. Go to your repository on GitHub
2. Click on **Settings** tab
3. Scroll down to **Pages** section in the left sidebar
4. Under **Source**, select **GitHub Actions**

### 2. Configure Branch Protection (Recommended)

To ensure quality deployments:

1. Go to **Settings** → **Branches**
2. Click **Add rule** for the `main` branch
3. Enable:
   - ✅ **Require status checks to pass before merging**
   - ✅ **Require branches to be up to date before merging**
   - ✅ **Require pull request reviews before merging**

### 3. Environment Configuration (Optional but Recommended)

For additional security and control:

1. Go to **Settings** → **Environments**
2. Click **New environment**
3. Name it `github-pages`
4. Configure protection rules:
   - **Required reviewers**: Add team members who should approve deployments
   - **Wait timer**: Set a delay before deployment (optional)
   - **Deployment branches**: Restrict to `main` branch only

## Workflow Permissions

The deployment workflow requires specific permissions that are automatically configured:

```yaml
permissions:
  contents: read    # Read repository contents
  pages: write      # Deploy to GitHub Pages
  id-token: write   # OIDC token for secure deployment
```

## Deployment Triggers

The demo site will be deployed automatically when:

1. **Push to main branch**: Automatic deployment after successful build and tests
2. **Pull request to main**: Build and test only (no deployment)
3. **Manual trigger**: Use the "Run workflow" button in GitHub Actions

### Manual Deployment

To manually deploy the demo site:

1. Go to **Actions** tab in your repository
2. Select **Deploy Demo Site to GitHub Pages** workflow
3. Click **Run workflow**
4. Choose the branch (should be `main`)
5. Optionally uncheck "Deploy to GitHub Pages" to build only
6. Click **Run workflow**

## Accessing the Demo Site

Once deployed, your demo site will be available at:

```
https://<username>.github.io/<repository-name>/
```

For example:
- Repository: `psprowls/design-system`
- Demo Site: `https://psprowls.github.io/design-system/`

## Troubleshooting

### Common Issues

#### 1. "Pages build and deployment" failing
- **Cause**: GitHub Pages source not set to "GitHub Actions"
- **Solution**: Go to Settings → Pages → Source → Select "GitHub Actions"

#### 2. 404 errors on demo site
- **Cause**: Incorrect base paths or missing files
- **Solution**: Check that `www/` directory contains `index.html` and all assets

#### 3. Workflow permission errors
- **Cause**: Insufficient permissions for GITHUB_TOKEN
- **Solution**: Ensure repository settings allow Actions to create Pages deployments

#### 4. Build failures
- **Cause**: Test failures or build errors
- **Solution**: Check the workflow logs and fix any failing tests or build issues

### Checking Deployment Status

1. Go to **Actions** tab
2. Look for the latest "Deploy Demo Site to GitHub Pages" workflow run
3. Check the status:
   - ✅ **Green**: Deployment successful
   - ❌ **Red**: Deployment failed (check logs)
   - 🟡 **Yellow**: Deployment in progress

### Viewing Deployment Logs

1. Click on the failed workflow run
2. Click on the **build** or **deploy** job
3. Expand the failing step to see detailed logs
4. Look for error messages and stack traces

## Security Considerations

### Repository Secrets

No additional secrets are required for GitHub Pages deployment. The workflow uses the built-in `GITHUB_TOKEN` which has the necessary permissions.

### Branch Protection

Consider enabling branch protection on `main` to:
- Require pull request reviews
- Require status checks (tests) to pass
- Prevent direct pushes to main

### Environment Protection

Use GitHub Environments to:
- Require manual approval for deployments
- Restrict deployments to specific branches
- Add deployment delays for additional safety

## Monitoring and Maintenance

### Regular Checks

- Monitor deployment success rates
- Review build times and optimize if needed
- Keep dependencies updated
- Test demo site functionality after deployments

### Performance Optimization

The demo site is optimized for GitHub Pages with:
- Production builds using `--prod` flag
- Minified assets and code
- Optimized images and fonts
- Efficient caching strategies

## Next Steps

After setting up GitHub Pages:

1. **Test the deployment** by pushing a change to main
2. **Verify the demo site** loads correctly
3. **Share the demo URL** with your team
4. **Set up monitoring** for deployment failures
5. **Document the demo site** in your project README

For more information, see:
- [GitHub Pages Documentation](https://docs.github.com/en/pages)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [PUBLISHING.md](./PUBLISHING.md) for package publishing
