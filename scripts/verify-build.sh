#!/bin/bash

# Design System - Build Verification Script
# This script verifies that the demo site build is complete and ready for deployment

set -e  # Exit on any error

echo "🔍 Verifying Design System build..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if file/directory exists
check_exists() {
    if [ -e "$1" ]; then
        echo -e "SUCCESS: ${GREEN}$1${NC}"
        return 0
    else
        echo -e "ERROR: ${RED}$1 (missing)${NC}"
        return 1
    fi
}

# Function to check file size
check_file_size() {
    if [ -f "$1" ]; then
        size=$(stat -f%z "$1" 2>/dev/null || stat -c%s "$1" 2>/dev/null || echo "0")
        if [ "$size" -gt 0 ]; then
            echo -e "SUCCESS: ${GREEN}$1 (${size} bytes)${NC}"
            return 0
        else
            echo -e "ERROR: ${RED}$1 (empty file)${NC}"
            return 1
        fi
    else
        echo -e "ERROR: ${RED}$1 (missing)${NC}"
        return 1
    fi
}

# Initialize error counter
errors=0

echo "📁 Checking directory structure..."
echo "-----------------------------------"

# Check main directories
check_exists "www" || ((errors++))
check_exists "www/build" || ((errors++))
check_exists "www/docs" || ((errors++))
check_exists "www/assets" || ((errors++))

echo ""
echo "📄 Checking critical files..."
echo "------------------------------"

# Check main HTML file
check_file_size "www/index.html" || ((errors++))

# Check build files
check_exists "www/build/gahr.esm.js" || ((errors++))
check_exists "www/build/gahr.js" || ((errors++))

# Check host config
check_file_size "www/host.config.json" || ((errors++))

echo ""
echo "📚 Checking documentation..."
echo "-----------------------------"

# Check component docs
check_exists "www/docs/components" || ((errors++))
check_file_size "www/docs/components/accordion.html" || ((errors++))
check_file_size "www/docs/components/button.html" || ((errors++))
check_file_size "www/docs/components/link.html" || ((errors++))
check_file_size "www/docs/components/logo.html" || ((errors++))
check_file_size "www/docs/components/monogram.html" || ((errors++))

# Check content docs
check_exists "www/docs/content" || ((errors++))
check_file_size "www/docs/content/colors.html" || ((errors++))
check_file_size "www/docs/content/typography.html" || ((errors++))

# Check page templates
check_exists "www/docs/page-templates" || ((errors++))
check_exists "www/docs/page-templates/grand-america" || ((errors++))
check_exists "www/docs/page-templates/little-america-slc" || ((errors++))

echo ""
echo "🎨 Checking assets..."
echo "---------------------"

# Check fonts
check_exists "www/assets/fonts" || ((errors++))

# Check component assets
check_exists "www/assets/ga" || ((errors++))
check_exists "www/assets/la-slc" || ((errors++))

echo ""
echo "🔧 Checking build artifacts..."
echo "-------------------------------"

# Count JavaScript files in build directory
if [ -d "www/build" ]; then
    js_count=$(find www/build -name "*.js" | wc -l)
    if [ "$js_count" -gt 0 ]; then
        echo -e "SUCCESS: ${GREEN}Found $js_count JavaScript files${NC}"
    else
        echo -e "ERROR: ${RED}No JavaScript files found in build directory${NC}"
        ((errors++))
    fi
    
    # Count CSS files
    css_count=$(find www/build -name "*.css" | wc -l)
    if [ "$css_count" -gt 0 ]; then
        echo -e "SUCCESS: ${GREEN}Found $css_count CSS files${NC}"
    else
        echo -e "WARN: ${YELLOW}No CSS files found (may be inlined)${NC}"
    fi
else
    echo -e "ERROR: ${RED}Build directory not found${NC}"
    ((errors++))
fi

echo ""
echo "📊 Build Summary"
echo "=================="

if [ $errors -eq 0 ]; then
    echo -e "SUCCESS: ${GREEN}All checks passed! Build is ready for deployment.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Test locally: npm start"
    echo "2. Deploy: Push to main branch or run workflow manually"
    echo "3. Verify: Check the live demo site after deployment"
    exit 0
else
    echo -e "FAILURE: ${RED}Found $errors error(s). Please fix before deploying.${NC}"
    echo ""
    echo "Common fixes:"
    echo "1. Run: npm run clean && npm run build.demo"
    echo "2. Check for build errors in the output"
    echo "3. Verify all source files are present"
    exit 1
fi
