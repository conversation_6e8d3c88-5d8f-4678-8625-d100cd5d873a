# Grand America Hotel and Resorts Design System

A comprehensive design system built with StencilJS, providing reusable components and design tokens for GAHR properties.

## 🚀 Demo Site

The design system demo site is automatically deployed to GitHub Pages and showcases all components, design tokens, and page templates.

**Demo Site URL:** [https://psprowls.github.io/design-system/](https://psprowls.github.io/design-system/)

### What's included in the demo:
- **Component Documentation**: Interactive examples of all design system components
- **Design Tokens**: Color palettes, typography, and spacing guidelines
- **Page Templates**: Complete page examples for Grand America and Little America properties
- **Brand Themes**: Examples showing how components adapt to different brand contexts

## 📦 Installation

```bash
npm install @psprowls/design-system --registry=https://npm.pkg.github.com
```

## 🛠️ Development

### Local Development
```bash
# Install dependencies
npm install

# Start development server
npm start

# Run tests
npm test
```

### Building
```bash
# Build the design system package
npm run build

# Build demo site for production
npm run build.demo

# Clean build artifacts
npm run clean
```

## 🚀 Deployment

### Demo Site Deployment
The demo site is automatically deployed to GitHub Pages when changes are pushed to the `main` branch. The deployment workflow:

1. **Builds** the demo site using `npm run build.demo`
2. **Tests** all components to ensure quality
3. **Deploys** to GitHub Pages using the latest GitHub Actions

You can also manually trigger a deployment:
1. Go to the **Actions** tab in GitHub
2. Select **Deploy Demo Site to GitHub Pages**
3. Click **Run workflow**

### Package Publishing
See [PUBLISHING.md](./PUBLISHING.md) for detailed instructions on publishing the design system package.

## 📚 Documentation

- [Publishing Guide](./PUBLISHING.md) - How to publish new versions
- [GitHub Pages Setup](./GITHUB-PAGES-SETUP.md) - Demo site deployment configuration
- [CI/CD Setup](./CI-CD-SETUP.md) - Continuous integration and deployment
- [GitHub Workflows](./GITHUB-WORKFLOWS.md) - Automated workflow documentation

## 🏗️ Architecture

Built with:
- **StencilJS** - Web component compiler
- **React Output Target** - React component generation
- **Tailwind CSS** - Utility-first CSS framework
- **DaisyUI** - Component library for Tailwind
- **TypeScript** - Type safety and developer experience