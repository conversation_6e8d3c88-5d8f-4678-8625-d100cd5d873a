name: Publish Design System Package

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run in dry-run mode (build only, no publish)'
        required: false
        default: false
        type: boolean

jobs:
  # Setup and validation
  setup:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      version: ${{ steps.package-version.outputs.version }}
      cache-key: ${{ steps.cache-key.outputs.key }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@psprowls'

      - name: Get package version
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Package version: $VERSION"

      - name: Check if version already exists
        id: version-check
        run: |
          PACKAGE_NAME="@psprowls/design-system"
          VERSION="${{ steps.package-version.outputs.version }}"

          # Check if version exists in GitHub Package Registry
          if npm view "$PACKAGE_NAME@$VERSION" --registry=https://npm.pkg.github.com 2>/dev/null; then
            echo "exists=true" >> $GITHUB_OUTPUT
            echo "Version $VERSION already exists in the registry"
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "Version $VERSION does not exist in the registry"
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Fail if version already exists
        if: steps.version-check.outputs.exists == 'true'
        run: |
          echo "ERROR: Version ${{ steps.package-version.outputs.version }} already exists in the GitHub Package Registry"
          echo "Please update the version in package.json before publishing"
          exit 1

      - name: Generate cache key
        id: cache-key
        run: echo "key=publish-${{ hashFiles('package-lock.json', 'src/**/*', 'stencil.config.ts') }}" >> $GITHUB_OUTPUT

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install dependencies
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          rm -rf node_modules package-lock.json
          npm install
          echo "Verifying Stencil installation..."
          ls -la node_modules/@stencil/core/cli/
          ls -la node_modules/.bin/stencil

      - name: Cache dependencies
        uses: actions/cache/save@v4
        with:
          path: node_modules/
          key: ${{ steps.cache-key.outputs.key }}

  # Run tests in parallel
  test:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Run tests
        run: npm test

  # Build package in parallel
  build:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Clean previous builds
        run: npm run clean

      - name: Build package
        run: npm run build

      - name: Verify build output
        run: |
          echo "Checking build output..."
          ls -la dist/
          echo "Verifying required files exist..."
          test -f dist/index.js || (echo "ERROR: dist/index.js not found" && exit 1)
          test -f dist/index.cjs.js || (echo "ERROR: dist/index.cjs.js not found" && exit 1)
          test -d dist/types || (echo "ERROR: dist/types directory not found" && exit 1)
          test -d dist/react || (echo "ERROR: dist/react directory not found" && exit 1)
          echo "INFO: All required build files are present"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 1

  # Publish package (waits for both test and build to complete)
  publish:
    runs-on: ubuntu-latest
    needs: [setup, test, build]
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@psprowls'

      - name: Restore dependencies
        uses: actions/cache/restore@v4
        with:
          path: node_modules/
          key: ${{ needs.setup.outputs.cache-key }}
          fail-on-cache-miss: true

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist/

      - name: Publish to GitHub Package Registry (Dry Run)
        if: inputs.dry_run == true
        run: |
          echo "INFO: Dry run mode - would publish version ${{ needs.setup.outputs.version }}"
          npm publish --dry-run --registry=https://npm.pkg.github.com
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish to GitHub Package Registry
        if: inputs.dry_run != true
        run: |
          echo "INFO: Publishing version ${{ needs.setup.outputs.version }} to GitHub Package Registry"
          npm publish --registry=https://npm.pkg.github.com
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create release summary
        if: inputs.dry_run != true
        run: |
          echo "## SUCCESS: Package Published Successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Package:** @psprowls/design-system" >> $GITHUB_STEP_SUMMARY
          echo "**Version:** ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "**Registry:** GitHub Package Registry" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Installation" >> $GITHUB_STEP_SUMMARY
          echo '```bash' >> $GITHUB_STEP_SUMMARY
          echo "npm install @psprowls/design-system@${{ needs.setup.outputs.version }} --registry=https://npm.pkg.github.com" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Update your web project to use the new version" >> $GITHUB_STEP_SUMMARY
          echo "- Test the integration in your development environment" >> $GITHUB_STEP_SUMMARY
          echo "- Consider updating documentation if there are breaking changes" >> $GITHUB_STEP_SUMMARY