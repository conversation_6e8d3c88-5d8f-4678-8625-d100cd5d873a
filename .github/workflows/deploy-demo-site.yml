name: Deploy Demo Site to GitHub Pages

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:
    inputs:
      deploy_to_pages:
        description: 'Deploy to GitHub Pages (only works on master branch)'
        required: false
        default: true
        type: boolean

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install dependencies
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          rm -rf node_modules package-lock.json
          npm install
          echo "Verifying Stencil installation..."
          ls -la node_modules/@stencil/core/cli/
          ls -la node_modules/.bin/stencil
        
      - name: Run tests
        run: npm test
        
      - name: Clean previous builds
        run: npm run clean
        
      - name: Build demo site
        run: npm run build.demo
        
      - name: Verify build output
        run: |
          echo "Running comprehensive build verification..."
          chmod +x scripts/verify-build.sh
          ./scripts/verify-build.sh
          
      - name: Setup Pages
        if: github.ref == 'refs/heads/master' && (github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && inputs.deploy_to_pages))
        uses: actions/configure-pages@v5
        
      - name: Upload artifact
        if: github.ref == 'refs/heads/master' && (github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && inputs.deploy_to_pages))
        uses: actions/upload-pages-artifact@v3
        with:
          path: './www'

  # Deployment job
  deploy:
    if: github.ref == 'refs/heads/master' && (github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && inputs.deploy_to_pages))
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        
      - name: Create deployment summary
        run: |
          echo "## SUCCESS: Demo Site Deployed Successfully" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Demo Site URL:** ${{ steps.deployment.outputs.page_url }}" >> $GITHUB_STEP_SUMMARY
          echo "**Repository:** ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### What was deployed" >> $GITHUB_STEP_SUMMARY
          echo "- Design System Demo Site" >> $GITHUB_STEP_SUMMARY
          echo "- Component Documentation" >> $GITHUB_STEP_SUMMARY
          echo "- Page Templates and Examples" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Visit the demo site to verify all components are working correctly" >> $GITHUB_STEP_SUMMARY
          echo "- Share the demo site URL with stakeholders for review" >> $GITHUB_STEP_SUMMARY
          echo "- Update any documentation that references the demo site URL" >> $GITHUB_STEP_SUMMARY
