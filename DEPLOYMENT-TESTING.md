# Deployment Testing Guide

This guide provides comprehensive testing procedures for the GitHub Pages deployment of the Design System demo site.

## Pre-Deployment Testing

### 1. Local Build Verification

Before deploying, always test the build locally:

```bash
# Clean previous builds
npm run clean

# Run tests
npm test

# Build demo site
npm run build.demo

# Verify build output
ls -la www/
```

**Expected output structure:**
```
www/
├── assets/           # Fonts and component assets
├── build/           # Compiled JavaScript and CSS
├── docs/            # Component and content documentation
├── host.config.json # Stencil configuration
└── index.html       # Main demo site page
```

### 2. Required Files Check

Verify these critical files exist:

```bash
# Main entry point
test -f www/index.html && echo "✅ index.html found" || echo "❌ index.html missing"

# Build directory
test -d www/build && echo "✅ build directory found" || echo "❌ build directory missing"

# Documentation
test -d www/docs && echo "✅ docs directory found" || echo "❌ docs directory missing"

# Assets
test -d www/assets && echo "✅ assets directory found" || echo "❌ assets directory missing"
```

### 3. Local Server Testing

Test the demo site locally before deployment:

```bash
# Start local development server
npm start

# Or serve the built www directory
npx http-server www -p 8080
```

Visit `http://localhost:8080` and verify:
- ✅ Main page loads without errors
- ✅ All component examples work
- ✅ Navigation between pages functions
- ✅ Assets (fonts, images) load correctly
- ✅ No console errors in browser dev tools

## Deployment Testing

### 1. Workflow Validation

Test the GitHub Actions workflow:

```bash
# Trigger workflow manually
# Go to GitHub → Actions → "Deploy Demo Site to GitHub Pages" → Run workflow
```

Monitor the workflow execution:
1. **Build job** should complete successfully
2. **Deploy job** should complete successfully
3. Check for any error messages in logs

### 2. Post-Deployment Verification

After successful deployment, verify the live site:

#### Basic Functionality
- [ ] Demo site loads at the GitHub Pages URL
- [ ] Main navigation works
- [ ] All component examples render correctly
- [ ] Page templates display properly
- [ ] No 404 errors for assets or pages

#### Component Testing
Visit each component page and verify:
- [ ] **Accordion**: Expand/collapse functionality works
- [ ] **Button**: All variants display correctly
- [ ] **Link**: Hover effects and navigation work
- [ ] **Logo**: Brand variants display correctly
- [ ] **Monogram**: All brand variants render
- [ ] **Page Header**: Navigation and branding work

#### Content Testing
- [ ] **Colors**: Color palettes display correctly
- [ ] **Typography**: Font families load and display
- [ ] **Page Templates**: Grand America and Little America themes work

#### Cross-Browser Testing
Test in multiple browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

#### Mobile Responsiveness
- [ ] Site works on mobile devices
- [ ] Navigation adapts to small screens
- [ ] Components are touch-friendly
- [ ] Text remains readable

## Automated Testing

### 1. GitHub Actions Checks

The deployment workflow includes automated checks:

```yaml
# Build verification
- Installs dependencies
- Runs all tests
- Builds demo site
- Verifies required files exist

# Deployment verification
- Uploads to GitHub Pages
- Confirms deployment success
- Provides deployment URL
```

### 2. Test Scripts

Add these test scripts to verify deployment:

```bash
# Test all components
npm test

# Test build process
npm run build.demo && echo "Build successful" || echo "Build failed"

# Test file structure
./scripts/verify-build.sh  # Create this script
```

### 3. Continuous Monitoring

Set up monitoring for:
- Deployment success/failure notifications
- Site availability monitoring
- Performance monitoring
- Error tracking

## Troubleshooting Common Issues

### Build Failures

**Symptom**: Workflow fails during build step
**Solutions**:
1. Check test failures: `npm test`
2. Verify dependencies: `npm ci`
3. Check for TypeScript errors
4. Review component code for issues

### Deployment Failures

**Symptom**: Build succeeds but deployment fails
**Solutions**:
1. Check GitHub Pages settings
2. Verify workflow permissions
3. Ensure repository is public or has Pages enabled
4. Check for file size limits

### 404 Errors on Live Site

**Symptom**: Demo site loads but pages/assets return 404
**Solutions**:
1. Verify file paths in HTML are correct
2. Check that all assets are in `www/` directory
3. Ensure case-sensitive file names match
4. Verify no hardcoded localhost URLs

### Styling Issues

**Symptom**: Components don't look correct on live site
**Solutions**:
1. Check that CSS files are included in build
2. Verify Tailwind CSS is compiled correctly
3. Check for missing font files
4. Verify asset paths are correct

### JavaScript Errors

**Symptom**: Components don't function on live site
**Solutions**:
1. Check browser console for errors
2. Verify all JavaScript files are loaded
3. Check for missing dependencies
4. Ensure Stencil components are defined

## Performance Testing

### 1. Load Time Testing

Use tools to measure performance:
- **Lighthouse**: Built into Chrome DevTools
- **PageSpeed Insights**: Google's web performance tool
- **WebPageTest**: Comprehensive performance testing

Target metrics:
- First Contentful Paint: < 2s
- Largest Contentful Paint: < 3s
- Cumulative Layout Shift: < 0.1

### 2. Asset Optimization

Verify assets are optimized:
- [ ] Images are compressed
- [ ] Fonts are subset if possible
- [ ] JavaScript is minified
- [ ] CSS is minified
- [ ] Unused code is removed

## Security Testing

### 1. Content Security Policy

Verify CSP headers are appropriate for GitHub Pages.

### 2. HTTPS

Ensure all resources load over HTTPS:
- [ ] No mixed content warnings
- [ ] All external resources use HTTPS
- [ ] No insecure requests in console

## Documentation Updates

After successful deployment:

1. **Update README**: Include live demo site URL
2. **Update documentation**: Reference live examples
3. **Share with team**: Notify stakeholders of new deployment
4. **Create release notes**: Document what was deployed

## Rollback Procedures

If deployment issues occur:

1. **Immediate**: Revert to previous working commit
2. **Fix issues**: Address problems in development
3. **Re-test**: Verify fixes work locally
4. **Re-deploy**: Push corrected version

## Monitoring and Maintenance

### Regular Checks
- Weekly: Verify demo site is accessible
- Monthly: Check for broken links
- Quarterly: Performance audit
- As needed: Update dependencies

### Alerts
Set up notifications for:
- Deployment failures
- Site downtime
- Performance degradation
- Security issues
