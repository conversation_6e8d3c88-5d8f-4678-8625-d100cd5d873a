# Example .npmrc configuration for consuming @psprowls/design-system
# Copy this file to your project root as .npmrc and update the token

# Set the registry for @psprowls scoped packages to GitHub Package Registry
@psprowls:registry=https://npm.pkg.github.com

# Authentication token for GitHub Package Registry
# Replace ${GITHUB_TOKEN} with your actual token or set it as an environment variable
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}

# Optional: Set default registry for all other packages (if not already configured)
# registry=https://registry.npmjs.org/
