# Integration Examples

This document provides examples of how to integrate the Design System into different types of web projects.

## React/Next.js Integration

### 1. Install the Package

```bash
npm install @psprowls/design-system@latest
```

### 2. Setup Custom Elements (Next.js)

```jsx
// app/layout.js (App Router) or pages/_app.js (Pages Router)
'use client'; // Only needed for App Router

import { useEffect } from 'react';
import { defineCustomElements } from '@psprowls/design-system/loader';

export default function RootLayout({ children }) {
  useEffect(() => {
    defineCustomElements();
  }, []);

  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
```

### 3. Use React Components

```jsx
// components/MyComponent.jsx
import { GahrButton, GahrCard, GahrRichText } from '@psprowls/design-system/react';

export default function MyComponent() {
  return (
    <div>
      <GahrCard>
        <GahrRichText>
          <h2>Welcome to GAHR</h2>
          <p>This is a card with rich text content.</p>
        </GahrRichText>
        <GahrButton 
          onClick={() => alert('Button clicked!')}
          buttonType="primary"
        >
          Book Now
        </GahrButton>
      </GahrCard>
    </div>
  );
}
```

## Vanilla JavaScript Integration

### 1. Install the Package

```bash
npm install @psprowls/design-system@latest
```

### 2. Import in Your JavaScript

```javascript
// main.js
import { defineCustomElements } from '@psprowls/design-system/loader';

// Define the custom elements
defineCustomElements();

// Now you can use the components in your HTML
document.addEventListener('DOMContentLoaded', () => {
  // Components are ready to use
  console.log('Design System loaded');
});
```

### 3. Use in HTML

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GAHR Website</title>
</head>
<body>
  <!-- Use GAHR components -->
  <gahr-page-header></gahr-page-header>
  
  <gahr-page-content>
    <div class="container mx-auto p-4">
      <gahr-card>
        <gahr-rich-text>
          <h1>Welcome to Grand America Hotel</h1>
          <p>Experience luxury and elegance in the heart of Salt Lake City.</p>
        </gahr-rich-text>
        
        <gahr-button button-type="primary">
          Book Your Stay
        </gahr-button>
      </gahr-card>
    </div>
  </gahr-page-content>
  
  <gahr-page-footer></gahr-page-footer>

  <script type="module" src="main.js"></script>
</body>
</html>
```

## CDN Integration (No Build Process)

### 1. Include via CDN

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GAHR Website</title>
  
  <!-- Include Design System from unpkg -->
  <script type="module" src="https://unpkg.com/@psprowls/design-system@latest/dist/gahr/gahr.esm.js"></script>
  <script nomodule src="https://unpkg.com/@psprowls/design-system@latest/dist/gahr/gahr.js"></script>
</head>
<body>
  <!-- Use components directly -->
  <gahr-page-header></gahr-page-header>
  
  <gahr-page-content>
    <gahr-rich-text>
      <h1>Welcome</h1>
      <p>This page uses the Design System via CDN.</p>
    </gahr-rich-text>
  </gahr-page-content>
  
  <gahr-page-footer></gahr-page-footer>
</body>
</html>
```

## TypeScript Support

The package includes full TypeScript definitions. No additional setup is required.

```typescript
// types are automatically available
import { GahrButton } from '@psprowls/design-system/react';

interface Props {
  onBooking: () => void;
}

const BookingComponent: React.FC<Props> = ({ onBooking }) => {
  return (
    <GahrButton 
      buttonType="primary" 
      onClick={onBooking}
    >
      Book Now
    </GahrButton>
  );
};
```

## Styling and Themes

The design system includes built-in themes for different GAHR properties:

```html
<!-- Grand America theme (default) -->
<html data-theme="ga">
  <body>
    <gahr-page-header></gahr-page-header>
  </body>
</html>

<!-- Little America SLC theme -->
<html data-theme="la-slc">
  <body>
    <gahr-page-header></gahr-page-header>
  </body>
</html>
```

## Package Updates

To update to a new version:

```bash
# Check current version
npm list @psprowls/design-system

# Update to latest
npm update @psprowls/design-system

# Or install specific version
npm install @psprowls/design-system@1.2.3
```

## Troubleshooting

### Components Not Rendering

1. Ensure `defineCustomElements()` is called
2. Check browser console for errors
3. Verify the package is properly installed

### TypeScript Errors

1. Ensure you're importing from the correct entry points
2. Check that TypeScript can find the type definitions
3. Restart your TypeScript server

### Styling Issues

1. Verify the correct theme is applied (`data-theme` attribute)
2. Check that CSS is being loaded properly
3. Ensure no conflicting styles are overriding the design system
