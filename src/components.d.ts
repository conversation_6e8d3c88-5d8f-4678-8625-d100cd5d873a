/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { NavItem } from "./components/page-header/page-header";
export { NavItem } from "./components/page-header/page-header";
export namespace Components {
    interface GahrAccordion {
    }
    interface GahrAccordionSection {
        /**
          * Accordion Name This is used to group the accordion sections together.
         */
        "accordionName": string;
        /**
          * Is Open This determines if the accordion section is open by default.
         */
        "isOpen": boolean;
        /**
          * Section Title This is the title of the accordion section that will be displayed.
         */
        "sectionTitle": string;
    }
    interface GahrButton {
    }
    interface GahrChevronDownIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrFacebookIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrInstagramIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrLink {
        /**
          * Link Href This is the URL that the link points to.
         */
        "href": string;
        /**
          * Link Class This is the class applied to the link element.
         */
        "linkClass": string;
        /**
          * Link Type This is the type of link to render.
         */
        "linkType": string;
    }
    interface GahrLinkedinIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrLogo {
        /**
          * CSS class to apply to the logo
         */
        "class"?: string;
    }
    interface GahrMonogram {
        /**
          * Alt text for accessibility
          * @default 'Monogram'
         */
        "alt"?: string;
        /**
          * CSS class to apply to the monogram
         */
        "class"?: string;
    }
    interface GahrPageContent {
    }
    interface GahrPageFooter {
    }
    interface GahrPageHeader {
        /**
          * @default defaultNav
         */
        "primaryNav": NavItem[];
    }
    interface GahrRichText {
    }
    interface GahrSearchIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrTriangleDownIcon {
        /**
          * @default 6
         */
        "size": number;
    }
    interface GahrXTwitterIcon {
        /**
          * @default 6
         */
        "size": number;
    }
}
declare global {
    interface HTMLGahrAccordionElement extends Components.GahrAccordion, HTMLStencilElement {
    }
    var HTMLGahrAccordionElement: {
        prototype: HTMLGahrAccordionElement;
        new (): HTMLGahrAccordionElement;
    };
    interface HTMLGahrAccordionSectionElement extends Components.GahrAccordionSection, HTMLStencilElement {
    }
    var HTMLGahrAccordionSectionElement: {
        prototype: HTMLGahrAccordionSectionElement;
        new (): HTMLGahrAccordionSectionElement;
    };
    interface HTMLGahrButtonElement extends Components.GahrButton, HTMLStencilElement {
    }
    var HTMLGahrButtonElement: {
        prototype: HTMLGahrButtonElement;
        new (): HTMLGahrButtonElement;
    };
    interface HTMLGahrChevronDownIconElement extends Components.GahrChevronDownIcon, HTMLStencilElement {
    }
    var HTMLGahrChevronDownIconElement: {
        prototype: HTMLGahrChevronDownIconElement;
        new (): HTMLGahrChevronDownIconElement;
    };
    interface HTMLGahrFacebookIconElement extends Components.GahrFacebookIcon, HTMLStencilElement {
    }
    var HTMLGahrFacebookIconElement: {
        prototype: HTMLGahrFacebookIconElement;
        new (): HTMLGahrFacebookIconElement;
    };
    interface HTMLGahrInstagramIconElement extends Components.GahrInstagramIcon, HTMLStencilElement {
    }
    var HTMLGahrInstagramIconElement: {
        prototype: HTMLGahrInstagramIconElement;
        new (): HTMLGahrInstagramIconElement;
    };
    interface HTMLGahrLinkElement extends Components.GahrLink, HTMLStencilElement {
    }
    var HTMLGahrLinkElement: {
        prototype: HTMLGahrLinkElement;
        new (): HTMLGahrLinkElement;
    };
    interface HTMLGahrLinkedinIconElement extends Components.GahrLinkedinIcon, HTMLStencilElement {
    }
    var HTMLGahrLinkedinIconElement: {
        prototype: HTMLGahrLinkedinIconElement;
        new (): HTMLGahrLinkedinIconElement;
    };
    interface HTMLGahrLogoElement extends Components.GahrLogo, HTMLStencilElement {
    }
    var HTMLGahrLogoElement: {
        prototype: HTMLGahrLogoElement;
        new (): HTMLGahrLogoElement;
    };
    interface HTMLGahrMonogramElement extends Components.GahrMonogram, HTMLStencilElement {
    }
    var HTMLGahrMonogramElement: {
        prototype: HTMLGahrMonogramElement;
        new (): HTMLGahrMonogramElement;
    };
    interface HTMLGahrPageContentElement extends Components.GahrPageContent, HTMLStencilElement {
    }
    var HTMLGahrPageContentElement: {
        prototype: HTMLGahrPageContentElement;
        new (): HTMLGahrPageContentElement;
    };
    interface HTMLGahrPageFooterElement extends Components.GahrPageFooter, HTMLStencilElement {
    }
    var HTMLGahrPageFooterElement: {
        prototype: HTMLGahrPageFooterElement;
        new (): HTMLGahrPageFooterElement;
    };
    interface HTMLGahrPageHeaderElement extends Components.GahrPageHeader, HTMLStencilElement {
    }
    var HTMLGahrPageHeaderElement: {
        prototype: HTMLGahrPageHeaderElement;
        new (): HTMLGahrPageHeaderElement;
    };
    interface HTMLGahrRichTextElement extends Components.GahrRichText, HTMLStencilElement {
    }
    var HTMLGahrRichTextElement: {
        prototype: HTMLGahrRichTextElement;
        new (): HTMLGahrRichTextElement;
    };
    interface HTMLGahrSearchIconElement extends Components.GahrSearchIcon, HTMLStencilElement {
    }
    var HTMLGahrSearchIconElement: {
        prototype: HTMLGahrSearchIconElement;
        new (): HTMLGahrSearchIconElement;
    };
    interface HTMLGahrTriangleDownIconElement extends Components.GahrTriangleDownIcon, HTMLStencilElement {
    }
    var HTMLGahrTriangleDownIconElement: {
        prototype: HTMLGahrTriangleDownIconElement;
        new (): HTMLGahrTriangleDownIconElement;
    };
    interface HTMLGahrXTwitterIconElement extends Components.GahrXTwitterIcon, HTMLStencilElement {
    }
    var HTMLGahrXTwitterIconElement: {
        prototype: HTMLGahrXTwitterIconElement;
        new (): HTMLGahrXTwitterIconElement;
    };
    interface HTMLElementTagNameMap {
        "gahr-accordion": HTMLGahrAccordionElement;
        "gahr-accordion-section": HTMLGahrAccordionSectionElement;
        "gahr-button": HTMLGahrButtonElement;
        "gahr-chevron-down-icon": HTMLGahrChevronDownIconElement;
        "gahr-facebook-icon": HTMLGahrFacebookIconElement;
        "gahr-instagram-icon": HTMLGahrInstagramIconElement;
        "gahr-link": HTMLGahrLinkElement;
        "gahr-linkedin-icon": HTMLGahrLinkedinIconElement;
        "gahr-logo": HTMLGahrLogoElement;
        "gahr-monogram": HTMLGahrMonogramElement;
        "gahr-page-content": HTMLGahrPageContentElement;
        "gahr-page-footer": HTMLGahrPageFooterElement;
        "gahr-page-header": HTMLGahrPageHeaderElement;
        "gahr-rich-text": HTMLGahrRichTextElement;
        "gahr-search-icon": HTMLGahrSearchIconElement;
        "gahr-triangle-down-icon": HTMLGahrTriangleDownIconElement;
        "gahr-x-twitter-icon": HTMLGahrXTwitterIconElement;
    }
}
declare namespace LocalJSX {
    interface GahrAccordion {
    }
    interface GahrAccordionSection {
        /**
          * Accordion Name This is used to group the accordion sections together.
         */
        "accordionName"?: string;
        /**
          * Is Open This determines if the accordion section is open by default.
         */
        "isOpen"?: boolean;
        /**
          * Section Title This is the title of the accordion section that will be displayed.
         */
        "sectionTitle"?: string;
    }
    interface GahrButton {
    }
    interface GahrChevronDownIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrFacebookIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrInstagramIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrLink {
        /**
          * Link Href This is the URL that the link points to.
         */
        "href"?: string;
        /**
          * Link Class This is the class applied to the link element.
         */
        "linkClass"?: string;
        /**
          * Link Type This is the type of link to render.
         */
        "linkType"?: string;
    }
    interface GahrLinkedinIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrLogo {
        /**
          * CSS class to apply to the logo
         */
        "class"?: string;
    }
    interface GahrMonogram {
        /**
          * Alt text for accessibility
          * @default 'Monogram'
         */
        "alt"?: string;
        /**
          * CSS class to apply to the monogram
         */
        "class"?: string;
    }
    interface GahrPageContent {
    }
    interface GahrPageFooter {
    }
    interface GahrPageHeader {
        /**
          * @default defaultNav
         */
        "primaryNav"?: NavItem[];
    }
    interface GahrRichText {
    }
    interface GahrSearchIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrTriangleDownIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface GahrXTwitterIcon {
        /**
          * @default 6
         */
        "size"?: number;
    }
    interface IntrinsicElements {
        "gahr-accordion": GahrAccordion;
        "gahr-accordion-section": GahrAccordionSection;
        "gahr-button": GahrButton;
        "gahr-chevron-down-icon": GahrChevronDownIcon;
        "gahr-facebook-icon": GahrFacebookIcon;
        "gahr-instagram-icon": GahrInstagramIcon;
        "gahr-link": GahrLink;
        "gahr-linkedin-icon": GahrLinkedinIcon;
        "gahr-logo": GahrLogo;
        "gahr-monogram": GahrMonogram;
        "gahr-page-content": GahrPageContent;
        "gahr-page-footer": GahrPageFooter;
        "gahr-page-header": GahrPageHeader;
        "gahr-rich-text": GahrRichText;
        "gahr-search-icon": GahrSearchIcon;
        "gahr-triangle-down-icon": GahrTriangleDownIcon;
        "gahr-x-twitter-icon": GahrXTwitterIcon;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "gahr-accordion": LocalJSX.GahrAccordion & JSXBase.HTMLAttributes<HTMLGahrAccordionElement>;
            "gahr-accordion-section": LocalJSX.GahrAccordionSection & JSXBase.HTMLAttributes<HTMLGahrAccordionSectionElement>;
            "gahr-button": LocalJSX.GahrButton & JSXBase.HTMLAttributes<HTMLGahrButtonElement>;
            "gahr-chevron-down-icon": LocalJSX.GahrChevronDownIcon & JSXBase.HTMLAttributes<HTMLGahrChevronDownIconElement>;
            "gahr-facebook-icon": LocalJSX.GahrFacebookIcon & JSXBase.HTMLAttributes<HTMLGahrFacebookIconElement>;
            "gahr-instagram-icon": LocalJSX.GahrInstagramIcon & JSXBase.HTMLAttributes<HTMLGahrInstagramIconElement>;
            "gahr-link": LocalJSX.GahrLink & JSXBase.HTMLAttributes<HTMLGahrLinkElement>;
            "gahr-linkedin-icon": LocalJSX.GahrLinkedinIcon & JSXBase.HTMLAttributes<HTMLGahrLinkedinIconElement>;
            "gahr-logo": LocalJSX.GahrLogo & JSXBase.HTMLAttributes<HTMLGahrLogoElement>;
            "gahr-monogram": LocalJSX.GahrMonogram & JSXBase.HTMLAttributes<HTMLGahrMonogramElement>;
            "gahr-page-content": LocalJSX.GahrPageContent & JSXBase.HTMLAttributes<HTMLGahrPageContentElement>;
            "gahr-page-footer": LocalJSX.GahrPageFooter & JSXBase.HTMLAttributes<HTMLGahrPageFooterElement>;
            "gahr-page-header": LocalJSX.GahrPageHeader & JSXBase.HTMLAttributes<HTMLGahrPageHeaderElement>;
            "gahr-rich-text": LocalJSX.GahrRichText & JSXBase.HTMLAttributes<HTMLGahrRichTextElement>;
            "gahr-search-icon": LocalJSX.GahrSearchIcon & JSXBase.HTMLAttributes<HTMLGahrSearchIconElement>;
            "gahr-triangle-down-icon": LocalJSX.GahrTriangleDownIcon & JSXBase.HTMLAttributes<HTMLGahrTriangleDownIconElement>;
            "gahr-x-twitter-icon": LocalJSX.GahrXTwitterIcon & JSXBase.HTMLAttributes<HTMLGahrXTwitterIconElement>;
        }
    }
}
