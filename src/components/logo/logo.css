/* Logo Component Styles */

gahr-logo {
  @apply inline-block w-full h-auto;
}

/* Remove old <img>-based styling; CSS mode only now */

/* CSS-mode rendering */
gahr-logo .logo {
  display: block;
  width: 100%;
  background-color: var(--logo-color, #000000);
  transition: background-color 200ms ease-in-out;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-position: center;
  mask-position: center;
}

/* Desktop mask assets (default) */
[data-theme="ga"] gahr-logo .logo {
  -webkit-mask-image: url('/assets/ga/logo-desktop-ga-black.svg');
  mask-image: url('/assets/ga/logo-desktop-ga-black.svg');
  aspect-ratio: 382.73 / 24.79;
}
[data-theme="la-slc"] gahr-logo .logo {
  -webkit-mask-image: url('/assets/la-slc/logo-desktop-la-slc-black.svg');
  mask-image: url('/assets/la-slc/logo-desktop-la-slc-black.svg');
  aspect-ratio: 364.76 / 57.47;
}

/* Mobile mask assets (override for small screens) */
@media (max-width: 768px) {
  [data-theme="ga"] gahr-logo .logo {
    -webkit-mask-image: url('/assets/ga/logo-mobile-ga-black.svg');
    mask-image: url('/assets/ga/logo-mobile-ga-black.svg');
    aspect-ratio: 212.2 / 134.21;
  }
  
  [data-theme="la-slc"] gahr-logo .logo {
    -webkit-mask-image: url('/assets/la-slc/logo-mobile-la-slc-black.svg');
    mask-image: url('/assets/la-slc/logo-mobile-la-slc-black.svg');
    aspect-ratio: 202.83 / 96.03;
  }
}

/* Base colors by brand + variant */
[data-theme="ga"] gahr-logo.primary .logo { --logo-color: var(--color-primary); }
[data-theme="ga"] gahr-logo.secondary .logo { --logo-color: var(--color-white); }
[data-theme="la-slc"] gahr-logo.primary .logo { --logo-color: var(--color-primary); }
[data-theme="la-slc"] gahr-logo.secondary .logo { --logo-color: var(--color-white); }

/* Hover swaps (only on devices that support hover) */
@media (hover: hover) and (pointer: fine) {
  [data-theme="ga"] gahr-logo.primary:hover .logo { --logo-color: var(--color-black); }
  [data-theme="ga"] gahr-logo.secondary:hover .logo { --logo-color: var(--color-primary); }
  [data-theme="la-slc"] gahr-logo.primary:hover .logo { --logo-color: var(--color-black); }
  [data-theme="la-slc"] gahr-logo.secondary:hover .logo { --logo-color: var(--color-primary); }
}
