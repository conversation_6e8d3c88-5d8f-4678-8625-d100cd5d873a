import { Component, h, Host, Prop } from '@stencil/core';

@Component({
  tag: 'gahr-chevron-down-icon',
  styleUrl: 'styles.css',
  shadow: false,
})
export class ChevronDownIcon {
  @Prop() size: number = 6;

  render() {
    return (
      <Host>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class={`size-${this.size}`}>
          <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
        </svg>
      </Host>
    );
  }
}
