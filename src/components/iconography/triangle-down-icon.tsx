import { Component, h, Host, Prop } from '@stencil/core';

@Component({
  tag: 'gahr-triangle-down-icon',
  styleUrl: 'styles.css',
  shadow: false,
})
export class TriangleDownIcon {
  @Prop() size: number = 6;

  render() {
    return (
      <Host>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5 3" fill="none" class={`size-${this.size}`}>
          <path d="M2.5 3L0.334936 6.90043e-08L4.66506 4.47556e-07L2.5 3Z" fill="black"/>
        </svg>
      </Host>
    );
  }
}
