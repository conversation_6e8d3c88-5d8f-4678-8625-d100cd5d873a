# GitHub Actions Workflows

This directory contains GitHub Actions workflows for the Design System project.

## Publish Package Workflow

The `publish-package.yml` workflow builds and publishes the Design System as an NPM package to GAHR's GitHub Package Registry.

### Features

- **Manual trigger only**: Uses `workflow_dispatch` - no automatic triggers
- **Version conflict prevention**: Fails if attempting to publish an existing version
- **Semantic versioning**: Uses version from `package.json`
- **Build verification**: Ensures all required files are built correctly
- **Dry run support**: Test the workflow without actually publishing
- **Comprehensive testing**: Runs tests before building and publishing

### How to Use

1. **Update the version** in `package.json` following semantic versioning:
   ```bash
   # For patch releases (bug fixes)
   npm version patch
   
   # For minor releases (new features)
   npm version minor
   
   # For major releases (breaking changes)
   npm version major
   ```

2. **Commit and push** the version change:
   ```bash
   git add package.json package-lock.json
   git commit -m "chore: bump version to x.x.x"
   git push
   ```

3. **Trigger the workflow**:
   - Go to the Actions tab in GitHub
   - Select "Publish Design System Package"
   - Click "Run workflow"
   - Optionally enable "dry run" to test without publishing

### Workflow Steps

1. **Checkout**: Gets the latest code
2. **Setup Node.js**: Configures Node.js 18 with GitHub Package Registry
3. **Version Check**: Prevents publishing duplicate versions
4. **Install Dependencies**: Runs `npm ci`
5. **Run Tests**: Executes `npm test`
6. **Clean & Build**: Runs `npm run clean` and `npm run build`
7. **Verify Build**: Ensures all required files are present
8. **Update package.json**: Configures for GitHub Package Registry
9. **Publish**: Publishes to GitHub Package Registry (unless dry run)
10. **Summary**: Creates a summary with installation instructions

### Required Permissions

The workflow requires these permissions (automatically configured):
- `contents: read` - To checkout the repository
- `packages: write` - To publish to GitHub Package Registry

### Authentication

The workflow uses the built-in `GITHUB_TOKEN` secret, which is automatically provided by GitHub Actions. No additional setup is required.

## Consuming the Published Package

### Installation

To install the Design System package in your web project:

```bash
# Install from GitHub Package Registry
npm install @psprowls/design-system@latest --registry=https://npm.pkg.github.com

# Or install a specific version
npm install @psprowls/design-system@1.0.0 --registry=https://npm.pkg.github.com
```

### Authentication for Installation

To install packages from GitHub Package Registry, you need to authenticate:

1. **Create a Personal Access Token** with `read:packages` permission
2. **Configure npm** to use the token:
   ```bash
   npm config set @psprowls:registry https://npm.pkg.github.com
   npm config set //npm.pkg.github.com/:_authToken YOUR_TOKEN_HERE
   ```

3. **Or use .npmrc file** in your project:
   ```
   @psprowls:registry=https://npm.pkg.github.com
   //npm.pkg.github.com/:_authToken=${NPM_TOKEN}
   ```

### Usage Examples

```javascript
// Import the entire design system
import '@psprowls/design-system';

// Import specific components (React)
import { GahrButton, GahrCard } from '@psprowls/design-system/react';

// Import the loader
import { defineCustomElements } from '@psprowls/design-system/loader';

// Define custom elements
defineCustomElements();
```

### Available Exports

The package provides multiple entry points:

- **Main**: `@psprowls/design-system` - Main bundle
- **React**: `@psprowls/design-system/react` - React components
- **Loader**: `@psprowls/design-system/loader` - Custom elements loader
- **Hydrate**: `@psprowls/design-system/hydrate` - SSR hydration

## Troubleshooting

### Version Already Exists Error

If you see "Version x.x.x already exists", update the version in `package.json`:

```bash
npm version patch  # or minor/major
git add package.json package-lock.json
git commit -m "chore: bump version"
git push
```

### Build Failures

Check that all required files are being built:
- `dist/index.js` and `dist/index.cjs.js`
- `dist/types/` directory with TypeScript definitions
- `dist/react/` directory with React components

### Authentication Issues

Ensure the workflow has proper permissions and that `GITHUB_TOKEN` is available. This should be automatic in GitHub Actions.

### Installation Issues

When consuming the package, ensure you have:
1. Proper authentication configured for GitHub Package Registry
2. Correct registry URL in your npm configuration
3. Valid Personal Access Token with `read:packages` permission
